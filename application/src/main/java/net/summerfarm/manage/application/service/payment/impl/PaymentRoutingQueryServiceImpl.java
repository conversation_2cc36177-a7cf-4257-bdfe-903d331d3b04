package net.summerfarm.manage.application.service.payment.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.manage.application.inbound.converter.payment.PaymentConverter;
import net.summerfarm.manage.application.service.payment.PaymentRoutingQueryService;
import net.summerfarm.manage.common.converter.PaymentPageInfoConvertor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.routing.common.enums.PaymentBusinessLineEnums;
import net.summerfarm.payment.routing.common.enums.PaymentChannelStatusEnums;
import net.summerfarm.payment.routing.common.enums.TenantIdEnums;
import net.summerfarm.payment.routing.model.dto.*;
import net.summerfarm.payment.routing.model.params.PaymentChannelQueryParams;
import net.summerfarm.payment.routing.model.params.PaymentRuleQueryParams;
import net.summerfarm.payment.routing.service.PaymentChannelService;
import net.summerfarm.payment.routing.service.PaymentRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 支付路由查询服务实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class PaymentRoutingQueryServiceImpl implements PaymentRoutingQueryService {

    @Resource
    private PaymentChannelService paymentChannelService;

    @Resource
    private PaymentRuleService paymentRuleService;

    @Override
    public PageInfo<PaymentChannelListDTO> pageListChannel(PaymentChannelQueryParams params) {
        PaymentChannelQueryDTO queryDTO = convertToPaymentChannelQueryDTO(params);
        queryDTO.setTenantId(TenantIdEnums.SUMMER_FARM.getTenantId());
        queryDTO.setBusinessLine(PaymentBusinessLineEnums.SUMMERFARM.getCode());

        try {
            PageInfo<PaymentChannelListDTO> result = paymentChannelService.pageListChannel(queryDTO);
            // 使用我们的兼容性转换器来确保PageInfo对象的兼容性
            return PaymentPageInfoConvertor.convert2PageInfoDTO(result);
        } catch (Exception e) {
            log.error("调用支付渠道分页查询服务时发生异常，参数：{}", queryDTO, e);
            // 返回空的分页结果
            return PaymentPageInfoConvertor.convert2PageInfoDTO(null);
        }
    }

    @Override
    public List<PaymentChannelListDTO> listChannel(PaymentChannelQueryParams params) {
        PaymentChannelQueryDTO queryDTO = convertToPaymentChannelQueryDTO(params);
        queryDTO.setTenantId(TenantIdEnums.SUMMER_FARM.getTenantId());
        queryDTO.setBusinessLine(PaymentBusinessLineEnums.SUMMERFARM.getCode());
        return paymentChannelService.listChannel(queryDTO);
    }

    @Override
    public PaymentChannelDetailDTO queryChannelDetail(Long id) {
        PaymentChannelIdDTO paymentChannelIdDTO = new PaymentChannelIdDTO();
        paymentChannelIdDTO.setId(id);
        return paymentChannelService.queryChannelDetail(paymentChannelIdDTO);
    }

    @Override
    public PageInfo<PaymentRuleListDTO> pageListRule(PaymentRuleQueryParams params) {
        PaymentRuleQueryDTO queryDTO = convertToPaymentRuleQueryDTO(params);
        queryDTO.setTenantId(TenantIdEnums.SUMMER_FARM.getTenantId());
        queryDTO.setBusinessLine(PaymentBusinessLineEnums.SUMMERFARM.getCode());

        try {
            PageInfo<PaymentRuleListDTO> result = paymentRuleService.pageListRule(queryDTO);
            // 使用我们的兼容性转换器来确保PageInfo对象的兼容性
            return PaymentPageInfoConvertor.convert2PageInfoDTO(result);
        } catch (Exception e) {
            log.error("调用支付规则分页查询服务时发生异常，参数：{}", queryDTO, e);
            // 返回空的分页结果
            return PaymentPageInfoConvertor.convert2PageInfoDTO(null);
        }
    }

    @Override
    public PaymentRuleDetailDTO queryRuleDetail(Long id) {
        return paymentRuleService.queryRuleDetail(id);
    }

    @Override
    public List<String> queryExistedCompanyEntity(Long tenantId, String businessLine) {
        PaymentChannelCompanyEntityQueryDTO dto = PaymentChannelCompanyEntityQueryDTO
                .builder()
                .tenantId(TenantIdEnums.SUMMER_FARM.getTenantId())
                .businessLine(PaymentBusinessLineEnums.SUMMERFARM.getCode())
                .statusList(Lists.newArrayList(PaymentChannelStatusEnums.getExistStatus()))
                .build();
        return paymentChannelService.queryCompanyEntities(dto);
    }

    private PaymentChannelQueryDTO convertToPaymentChannelQueryDTO(PaymentChannelQueryParams params) {
        return PaymentConverter.convert2ChannelQueryDTO(params);
    }

    private PaymentRuleQueryDTO convertToPaymentRuleQueryDTO(PaymentRuleQueryParams params) {
        return PaymentConverter.convert2RuleQueryDTO(params);
    }
}
